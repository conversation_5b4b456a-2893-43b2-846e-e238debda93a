import { api } from '@/convex/_generated/api';
import { Feather } from '@expo/vector-icons';
import { useMutation, useQuery } from 'convex/react';
import { useLocalSearchParams, useRouter } from 'expo-router';
import React, { useEffect, useRef, useState } from 'react';
import {
    Alert,
    Animated,
    Dimensions,
    PanResponder,
    Pressable,
    SafeAreaView,
    ScrollView,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    View,
    useColorScheme,
} from 'react-native';

const { height: SCREEN_HEIGHT } = Dimensions.get('window');

const INTERACTION_TYPES = [
  { value: 'call', label: 'Phone Call', icon: 'phone' },
  { value: 'email', label: 'Email', icon: 'mail' },
  { value: 'meeting', label: 'Meeting', icon: 'users' },
  { value: 'text', label: 'Text/SMS', icon: 'message-square' },
  { value: 'social', label: 'Social Media', icon: 'share-2' },
  { value: 'other', label: 'Other', icon: 'more-horizontal' },
];

export default function EditContactHistory() {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const router = useRouter();
  const { id, historyId } = useLocalSearchParams<{ id: string; historyId: string }>();
  
  const history = useQuery(api.contactHistory.getContactHistoryById, historyId ? { id: historyId } : 'skip');
  const updateContactHistory = useMutation(api.contactHistory.updateContactHistory);
  
  const slideAnim = useRef(new Animated.Value(SCREEN_HEIGHT)).current;
  
  const [formData, setFormData] = useState({
    title: '',
    notes: '',
    interactionType: 'call',
    location: '',
    duration: '',
    followUpRequired: false,
    followUpDate: '',
  });
  
  const [tags, setTags] = useState<string[]>([]);
  const [itemsDiscussed, setItemsDiscussed] = useState<string[]>([]);
  const [newTag, setNewTag] = useState('');
  const [newItem, setNewItem] = useState('');
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (history) {
      setFormData({
        title: history.title || '',
        notes: history.notes || '',
        interactionType: history.interactionType || 'call',
        location: history.location || '',
        duration: history.duration ? history.duration.toString() : '',
        followUpRequired: history.followUpRequired || false,
        followUpDate: history.followUpDate ? new Date(history.followUpDate).toISOString().split('T')[0] : '',
      });
      setTags(history.tags || []);
      setItemsDiscussed(history.itemsDiscussed || []);
      setSelectedDate(new Date(history.dateTime));
      setIsLoading(false);
    }
  }, [history]);

  useEffect(() => {
    Animated.spring(slideAnim, {
      toValue: 0,
      useNativeDriver: true,
      tension: 80,
      friction: 8,
    }).start();
  }, []);

  const panResponder = useRef(
    PanResponder.create({
      onMoveShouldSetPanResponder: (evt, gestureState) => {
        return gestureState.dy > 5;
      },
      onPanResponderMove: (evt, gestureState) => {
        if (gestureState.dy > 0) {
          slideAnim.setValue(gestureState.dy);
        }
      },
      onPanResponderRelease: (evt, gestureState) => {
        if (gestureState.dy > 100 || gestureState.vy > 0.3) {
          handleClose();
        } else {
          Animated.spring(slideAnim, {
            toValue: 0,
            useNativeDriver: true,
          }).start();
        }
      },
    })
  ).current;

  const handleClose = () => {
    Animated.timing(slideAnim, {
      toValue: SCREEN_HEIGHT,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      router.back();
    });
  };

  const handleUpdateHistory = async () => {
    try {
      if (!formData.title.trim()) {
        Alert.alert('Error', 'Title is required');
        return;
      }

      if (!historyId) {
        Alert.alert('Error', 'History ID is missing');
        return;
      }

      const updateData = {
        id: historyId,
        title: formData.title,
        notes: formData.notes || undefined,
        interactionType: formData.interactionType,
        tags: tags.length > 0 ? tags : undefined,
        itemsDiscussed: itemsDiscussed.length > 0 ? itemsDiscussed : undefined,
        location: formData.location || undefined,
        dateTime: selectedDate.getTime(),
        duration: formData.duration ? parseInt(formData.duration) : undefined,
        followUpRequired: formData.followUpRequired,
        followUpDate: formData.followUpDate ? new Date(formData.followUpDate).getTime() : undefined,
      };

      await updateContactHistory(updateData);
      
      Alert.alert('Success', 'Contact history updated successfully!', [
        { text: 'OK', onPress: () => handleClose() }
      ]);
    } catch (error) {
      Alert.alert('Error', 'Failed to update contact history');
      console.error(error);
    }
  };

  const updateFormData = (field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const addTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      setTags([...tags, newTag.trim()]);
      setNewTag('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  const addItem = () => {
    if (newItem.trim() && !itemsDiscussed.includes(newItem.trim())) {
      setItemsDiscussed([...itemsDiscussed, newItem.trim()]);
      setNewItem('');
    }
  };

  const removeItem = (itemToRemove: string) => {
    setItemsDiscussed(itemsDiscussed.filter(item => item !== itemToRemove));
  };

  const animatedStyle = {
    transform: [{ translateY: slideAnim }],
  };

  const styles = createStyles(isDark);

  if (isLoading) {
    return (
      <View style={[styles.container, { justifyContent: 'center', alignItems: 'center' }]}>
        <Text style={styles.label}>Loading...</Text>
      </View>
    );
  }

  return (
    <Animated.View style={[styles.container, animatedStyle]}>
      <SafeAreaView style={styles.scrollView}>
        {/* Header */}
        <View style={styles.header}>
          <Pressable onPress={handleClose} style={styles.closeButton}>
            <Text style={styles.closeButtonText}>✕</Text>
          </Pressable>
          <Text style={styles.headerTitle}>Edit Interaction</Text>
          <Pressable onPress={handleUpdateHistory} style={styles.createButton}>
            <Text style={styles.createButtonText}>Save</Text>
          </Pressable>
        </View>

        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          {/* Title */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Title *</Text>
            <TextInput
              style={styles.input}
              placeholder="e.g., Weekly check-in call"
              value={formData.title}
              onChangeText={(value) => updateFormData('title', value)}
            />
          </View>

          {/* Interaction Type */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Interaction Type</Text>
            <View style={styles.typeGrid}>
              {INTERACTION_TYPES.map((type) => (
                <TouchableOpacity
                  key={type.value}
                  style={[
                    styles.typeButton,
                    formData.interactionType === type.value ? styles.typeButtonActive : {}
                  ]}
                  onPress={() => updateFormData('interactionType', type.value)}
                >
                  <Feather 
                    name={type.icon as any} 
                    size={20} 
                    color={formData.interactionType === type.value ? '#fff' : (isDark ? '#fff' : '#000')} 
                  />
                  <Text style={[
                    styles.typeButtonText,
                    formData.interactionType === type.value ? styles.typeButtonTextActive : {}
                  ]}>
                    {type.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Date & Time */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Date & Time</Text>
            <TouchableOpacity 
              style={styles.input}
              onPress={() => {
                // TODO: Implement date/time picker
                console.log('Open date picker');
              }}
            >
              <Text style={styles.dateText}>
                {selectedDate.toLocaleDateString()} at {selectedDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
              </Text>
            </TouchableOpacity>
          </View>

          {/* Duration */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Duration (minutes)</Text>
            <TextInput
              style={styles.input}
              placeholder="e.g., 30"
              value={formData.duration}
              onChangeText={(value) => updateFormData('duration', value)}
              keyboardType="numeric"
            />
          </View>

          {/* Location */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Location</Text>
            <TextInput
              style={styles.input}
              placeholder="e.g., Zoom, Coffee shop, Office"
              value={formData.location}
              onChangeText={(value) => updateFormData('location', value)}
            />
          </View>

          {/* Items Discussed */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Items Discussed</Text>
            <View style={styles.chipContainer}>
              {itemsDiscussed.map((item, index) => (
                <View key={index} style={styles.chip}>
                  <Text style={styles.chipText}>{item}</Text>
                  <TouchableOpacity onPress={() => removeItem(item)} style={styles.chipRemove}>
                    <Feather name="x" size={16} color={isDark ? '#fff' : '#000'} />
                  </TouchableOpacity>
                </View>
              ))}
            </View>
            <View style={styles.addInputContainer}>
              <TextInput
                style={styles.addInput}
                placeholder="Add item..."
                value={newItem}
                onChangeText={setNewItem}
                onSubmitEditing={addItem}
              />
              <TouchableOpacity style={styles.addButton} onPress={addItem}>
                <Feather name="plus" size={16} color="#fff" />
              </TouchableOpacity>
            </View>
          </View>

          {/* Tags */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Tags</Text>
            <View style={styles.chipContainer}>
              {tags.map((tag, index) => (
                <View key={index} style={styles.chip}>
                  <Text style={styles.chipText}>{tag}</Text>
                  <TouchableOpacity onPress={() => removeTag(tag)} style={styles.chipRemove}>
                    <Feather name="x" size={16} color={isDark ? '#fff' : '#000'} />
                  </TouchableOpacity>
                </View>
              ))}
            </View>
            <View style={styles.addInputContainer}>
              <TextInput
                style={styles.addInput}
                placeholder="Add tag..."
                value={newTag}
                onChangeText={setNewTag}
                onSubmitEditing={addTag}
              />
              <TouchableOpacity style={styles.addButton} onPress={addTag}>
                <Feather name="plus" size={16} color="#fff" />
              </TouchableOpacity>
            </View>
          </View>

          {/* Notes */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Notes</Text>
            <TextInput
              style={[styles.input, styles.notesInput]}
              placeholder="Detailed notes about the interaction..."
              value={formData.notes}
              onChangeText={(value) => updateFormData('notes', value)}
              multiline
            />
          </View>

          {/* Follow-up Required */}
          <View style={styles.inputGroup}>
            <View style={styles.checkboxContainer}>
              <TouchableOpacity
                style={[styles.checkbox, formData.followUpRequired ? styles.checkboxChecked : {}]}
                onPress={() => updateFormData('followUpRequired', !formData.followUpRequired)}
              >
                {formData.followUpRequired && (
                  <Feather name="check" size={12} color="#fff" />
                )}
              </TouchableOpacity>
              <Text style={styles.checkboxLabel}>
                Follow-up required
              </Text>
            </View>
          </View>

          {/* Follow-up Date */}
          {formData.followUpRequired && (
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Follow-up Date</Text>
              <TouchableOpacity 
                style={styles.input}
                onPress={() => {
                  // TODO: Implement date picker
                  console.log('Open follow-up date picker');
                }}
              >
                <Text style={styles.dateText}>
                  {formData.followUpDate}
                </Text>
              </TouchableOpacity>
            </View>
          )}

          <View style={{ height: 40 }} />
        </ScrollView>
      </SafeAreaView>
    </Animated.View>
  );
}

function createStyles(isDark: boolean) {
  return StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: isDark ? '#000' : '#fff',
    },
    scrollView: {
      flex: 1,
      paddingHorizontal: 20,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 16,
      borderBottomWidth: 1,
      borderBottomColor: isDark ? '#333' : '#e0e0e0',
      marginBottom: 20,
    },
    closeButton: {
      position: 'absolute',
      left: 0,
      padding: 8,
    },
    closeButtonText: {
      fontSize: 24,
      color: isDark ? '#fff' : '#000',
    },
    headerTitle: {
      fontSize: 20,
      fontWeight: '600',
      color: isDark ? '#fff' : '#000',
    },
    inputGroup: {
      marginBottom: 24,
    },
    label: {
      fontSize: 16,
      fontWeight: '600',
      marginBottom: 8,
      color: isDark ? '#fff' : '#000',
    },
    input: {
      backgroundColor: isDark ? '#1a1a1a' : '#f8f8f8',
      borderRadius: 12,
      paddingHorizontal: 16,
      paddingVertical: 12,
      fontSize: 16,
      color: isDark ? '#fff' : '#000',
      borderWidth: 1,
      borderColor: isDark ? '#333' : '#e0e0e0',
    },
    notesInput: {
      minHeight: 100,
      textAlignVertical: 'top',
    },
    typeGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 8,
    },
    typeButton: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 16,
      paddingVertical: 12,
      borderRadius: 8,
      borderWidth: 1,
      borderColor: isDark ? '#333' : '#e0e0e0',
      marginRight: 8,
      marginBottom: 8,
      gap: 8,
    },
    typeButtonActive: {
      backgroundColor: '#007AFF',
      borderColor: '#007AFF',
    },
    typeButtonText: {
      fontSize: 14,
      color: isDark ? '#fff' : '#000',
    },
    typeButtonTextActive: {
      color: '#fff',
    },
    checkboxContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 16,
    },
    checkbox: {
      width: 20,
      height: 20,
      borderRadius: 4,
      borderWidth: 1,
      borderColor: isDark ? '#333' : '#e0e0e0',
      marginRight: 12,
      justifyContent: 'center',
      alignItems: 'center',
    },
    checkboxChecked: {
      backgroundColor: '#007AFF',
      borderColor: '#007AFF',
    },
    checkboxLabel: {
      fontSize: 16,
      color: isDark ? '#fff' : '#000',
    },
    chipContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      marginBottom: 12,
    },
    chip: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: isDark ? '#333' : '#f0f0f0',
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 16,
      marginRight: 8,
      marginBottom: 8,
    },
    chipText: {
      fontSize: 14,
      color: isDark ? '#fff' : '#000',
      marginRight: 4,
    },
    chipRemove: {
      padding: 2,
    },
    addInputContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    addInput: {
      flex: 1,
      backgroundColor: isDark ? '#1a1a1a' : '#f8f8f8',
      borderRadius: 12,
      paddingHorizontal: 16,
      paddingVertical: 12,
      fontSize: 16,
      color: isDark ? '#fff' : '#000',
      borderWidth: 1,
      borderColor: isDark ? '#333' : '#e0e0e0',
      marginRight: 8,
    },
    addButton: {
      width: 36,
      height: 36,
      borderRadius: 8,
      backgroundColor: '#007AFF',
      justifyContent: 'center',
      alignItems: 'center',
    },
    dateText: {
      fontSize: 16,
      color: isDark ? '#fff' : '#000',
    },
    createButton: {
      margin: 20,
      backgroundColor: '#007AFF',
      borderRadius: 12,
      paddingVertical: 16,
      alignItems: 'center',
    },
    createButtonText: {
      color: '#fff',
      fontSize: 18,
      fontWeight: '600',
    },
  });
}
